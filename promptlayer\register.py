# pip install selenium requests Faker
# 不走协议注册，当然是为了让佬一分钱都不花啊...

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager
import requests
import json
import random
import string
from faker import Faker
import time
import multiprocessing
from multiprocessing import Manager
import logging
import os
import re
import urllib3

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

fk = Faker()

# 邮箱配置
RECEIVE_EMAIL = "<EMAIL>"  # 接收邮件的邮箱
REGISTER_EMAIL_DOMAIN = "@kiechck.top"  # 注册时使用的邮箱后缀


def generate_register_email() -> str:
    """
    生成用于注册的邮箱地址

    Returns:
        str: 注册用的邮箱地址 (使用@kiechck.top后缀)
    """
    # 生成随机前缀
    import random
    import string

    # 生成8-12位随机字符串作为邮箱前缀
    prefix_length = random.randint(8, 12)
    prefix = ''.join(random.choices(string.ascii_lowercase + string.digits, k=prefix_length))

    return f"{prefix}{REGISTER_EMAIL_DOMAIN}"


def get_mail_list(email: str, first_id: int = 0) -> dict:
    """
    获取邮件列表

    Args:
        email: 邮箱地址
        first_id: 起始邮件ID

    Returns:
        dict: 邮件列表响应
    """
    url = f"https://tempmail.plus/api/mails"

    headers = {
        'accept': 'application/json, text/javascript, */*; q=0.01',
        'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',
        'priority': 'u=1, i',
        'referer': 'https://tempmail.plus/zh/',
        'sec-ch-ua': '"Chromium";v="136", "Microsoft Edge";v="136", "Not.A/Brand";v="99"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
        'sec-fetch-dest': 'empty',
        'sec-fetch-mode': 'cors',
        'sec-fetch-site': 'same-origin',
        'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
        'x-requested-with': 'XMLHttpRequest'
    }

    cookies = {
        'email': email.replace('@', '%40')
    }

    params = {
        'email': email,
        'first_id': first_id,
        'epin': ''
    }

    try:
        response = requests.get(url, headers=headers, cookies=cookies, params=params, timeout=10)
        response.raise_for_status()
        return response.json()
    except Exception as e:
        logger.warning(f"获取邮件列表失败: {str(e)}")
        return {"result": False, "error": str(e)}


def get_mail_details(mail_id: int, email: str) -> dict:
    """
    获取邮件详情

    Args:
        mail_id: 邮件ID
        email: 邮箱地址

    Returns:
        dict: 邮件详情响应
    """
    url = f"https://tempmail.plus/api/mails/{mail_id}"

    headers = {
        'accept': 'application/json, text/javascript, */*; q=0.01',
        'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',
        'priority': 'u=1, i',
        'referer': 'https://tempmail.plus/zh/',
        'sec-ch-ua': '"Chromium";v="136", "Microsoft Edge";v="136", "Not.A/Brand";v="99"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
        'sec-fetch-dest': 'empty',
        'sec-fetch-mode': 'cors',
        'sec-fetch-site': 'same-origin',
        'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
        'x-requested-with': 'XMLHttpRequest'
    }

    cookies = {
        'email': email.replace('@', '%40')
    }

    params = {
        'email': email,
        'epin': ''
    }

    try:
        response = requests.get(url, headers=headers, cookies=cookies, params=params, timeout=10)
        response.raise_for_status()
        return response.json()
    except Exception as e:
        logger.warning(f"获取邮件详情失败: {str(e)}")
        return {"result": False, "error": str(e)}


def extract_verification_code(html: str) -> str:
    """
    从验证邮件的HTML内容中提取验证码

    Args:
        html: 邮件的HTML内容

    Returns:
        str: 提取到的验证码，如果未找到则返回None
    """
    # 尝试匹配新的HTML格式中的验证码 (6位数字)
    pattern = r'<span style="[^"]*">\s*(\d{6})\s*</span>'
    match = re.search(pattern, html)

    if match:
        return match.group(1)

    # 备用模式：匹配任何6位连续数字
    pattern_backup = r'\b(\d{6})\b'
    match_backup = re.search(pattern_backup, html)

    if match_backup:
        return match_backup.group(1)

    return None


def wait_for_verification_email(email: str, target_email: str, max_wait_time: int = 60) -> str:
    """
    等待并获取验证邮件中的验证码

    Args:
        email: 临时邮箱地址
        target_email: 目标邮箱地址（用于验证）
        max_wait_time: 最大等待时间（秒）

    Returns:
        str: 验证码，如果未找到则返回None
    """
    start_time = time.time()
    retry_count = 0
    max_retries = 3

    logger.info(f"开始等待验证邮件，目标邮箱: {target_email}")

    while time.time() - start_time < max_wait_time:
        try:
            # 获取邮件列表
            mail_list_response = get_mail_list(email)

            if mail_list_response.get('result') and mail_list_response.get('mail_list'):
                logger.info(f"找到 {len(mail_list_response['mail_list'])} 封邮件")

                # 检查前三封邮件
                for i, mail in enumerate(mail_list_response['mail_list'][:3]):
                    logger.info(f"检查第 {i+1} 封邮件，ID: {mail['mail_id']}")
                    mail_id = mail['mail_id']

                    # 获取邮件详情
                    mail_details = get_mail_details(mail_id, email)

                    if mail_details.get('result'):
                        to_email = mail_details.get('to', '')
                        logger.info(f"邮件收件人: {to_email}")

                        # 验证邮件的to字段是否匹配目标邮箱
                        if to_email == target_email:
                            logger.info(f"找到匹配的验证邮件!")
                            # 提取验证码
                            html_content = mail_details.get('html', '')
                            verification_code = extract_verification_code(html_content)

                            if verification_code:
                                logger.info(f"成功提取验证码: {verification_code}")
                                return verification_code
                            else:
                                logger.warning(f"邮件中未找到验证码")
                        else:
                            logger.info(f"邮件收件人不匹配，跳过")
                    else:
                        logger.warning(f"获取邮件详情失败")
            else:
                logger.info(f"暂无邮件或获取失败")

            # 等待5秒后重试
            logger.info(f"等待5秒后重试...")
            time.sleep(5)
            retry_count = 0  # 重置重试计数

        except Exception as e:
            retry_count += 1
            logger.warning(f"获取邮件时出错 (第{retry_count}次): {str(e)}")

            if retry_count >= max_retries:
                logger.error(f"连续{max_retries}次失败，等待10秒后继续")
                time.sleep(10)
                retry_count = 0
            else:
                time.sleep(3)

    logger.warning(f"在 {max_wait_time} 秒内未找到验证邮件")
    return None


# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(processName)s - %(levelname)s - %(message)s",
    datefmt="%Y-%m-%d %H:%M:%S",
)
logger = logging.getLogger(__name__)


def generate_random_name():
    """生成随机的美国人名"""
    fake = Faker("en_US")
    return fake.name()


def generate_random_email(name=None):
    """生成随机的电子邮箱"""
    fake = Faker()
    if name:
        # 基于姓名生成邮箱前缀
        name_parts = name.lower().split()
        prefix = name_parts[0] + (name_parts[-1][0] if len(name_parts) > 1 else "")
        prefix += str(random.randint(1, 99999))
    else:
        prefix = fake.user_name() + str(random.randint(1, 9999))

    domains = [
        "gmail.com",
        "outlook.com",
        "yahoo.com",
        "hotmail.com",
        "icloud.com",
        "ucl.edu",
        "standford.edu",
    ]
    domain = random.choice(domains)

    return f"{prefix}@{REGISTER_EMAIL_DOMAIN}"


def generate_random_password(length=12):
    """生成随机密码，包含大小写字母、数字和特殊字符"""
    lowercase = string.ascii_lowercase
    uppercase = string.ascii_uppercase
    digits = string.digits
    special = "!@#$%^&*()-_=+[]{}|;:,.<>?"

    # 确保密码至少包含每种字符
    pwd = [
        random.choice(lowercase),
        random.choice(uppercase),
        random.choice(digits),
        random.choice(special),
    ]

    # 填充剩余长度
    remaining_length = length - len(pwd)
    all_chars = lowercase + uppercase + digits + special
    pwd.extend(random.choice(all_chars) for _ in range(remaining_length))

    # 打乱密码顺序
    random.shuffle(pwd)
    return "".join(pwd)


def generate_random_user():
    """生成随机用户信息，包括姓名、邮箱和密码"""
    name = generate_random_name()
    email = generate_random_email(name)
    password = generate_random_password()

    return {"name": name, "email": email, "password": password}


def login(email, password):
    """登录并获取访问令牌"""
    try:
        url = "https://api.promptlayer.com/login"
        data = {"email": email, "password": password}
        response = requests.post(url, json=data, timeout=10)
        response.raise_for_status()  # 检查 HTTP 错误
        return response.json()
    except (requests.RequestException, json.JSONDecodeError) as e:
        logger.error(f"登录失败 {email}: {str(e)}")
        return {"error": str(e)}


def save_account_info(account_info):
    """安全地将账户信息保存到文件"""
    try:
        with open("promptlayer.txt", "a+", encoding="utf-8") as f:
            f.write(f"{account_info}\n")
        return True
    except Exception as e:
        logger.error(f"保存账户信息时出错: {str(e)}")
        return False


def register(_):
    """注册 PromptLayer 账户

    注意：参数 _ 用于接收 pool.map 传入的索引，不使用
    """
    # 生成注册用的邮箱（@kiechck.top后缀）
    # register_email = generate_register_email()

    user_info = generate_random_user()
    password = user_info["password"]
    name = user_info["name"]
    register_email = user_info["email"]

    driver = None
    process_id = multiprocessing.current_process().name

    try:
        logger.info(f"进程 {process_id} 开始为 {register_email} 注册账户")

        options = webdriver.ChromeOptions()
        # 添加性能优化选项
        options.add_argument("--disable-gpu")
        options.add_argument("--no-sandbox")
        options.add_argument("--disable-dev-shm-usage")
        options.add_argument("--disable-web-security")
        options.add_argument("--disable-features=VizDisplayCompositor")
        options.add_argument("--ignore-certificate-errors")
        options.add_argument("--ignore-ssl-errors")
        options.add_argument("--allow-running-insecure-content")
        # 网络相关配置
        options.add_argument("--disable-extensions")
        options.add_argument("--disable-plugins")
        options.add_argument("--disable-images")
        # 可选：无头模式
        # options.add_argument('--headless')

        # 使用webdriver-manager自动管理ChromeDriver
        try:
            service = Service(ChromeDriverManager().install())
            driver = webdriver.Chrome(service=service, options=options)
        except Exception as e:
            logger.error(f"ChromeDriver初始化失败: {str(e)}")
            # 尝试使用系统PATH中的chromedriver
            try:
                driver = webdriver.Chrome(options=options)
            except Exception as e2:
                logger.error(f"使用系统ChromeDriver也失败: {str(e2)}")
                raise e2
        # 设置页面加载超时
        driver.set_page_load_timeout(60)
        driver.implicitly_wait(10)

        # 测试网络连接
        logger.info(f"测试网络连接...")
        try:
            # 先访问一个简单的页面测试连接
            driver.get("https://www.google.com")
            logger.info(f"网络连接正常")
        except Exception as e:
            logger.warning(f"网络连接测试失败: {str(e)}")
            # 继续尝试，可能只是google被阻止

        # 访问注册页面，增加重试机制
        max_retries = 3
        for attempt in range(max_retries):
            try:
                logger.info(f"尝试访问注册页面 (第{attempt+1}次)")
                driver.get("https://dashboard.promptlayer.com/create-account")
                logger.info(f"成功访问注册页面")
                break
            except Exception as e:
                logger.warning(f"访问注册页面失败 (第{attempt+1}次): {str(e)}")
                if attempt == max_retries - 1:
                    raise e
                time.sleep(5)  # 等待5秒后重试

        # 填写表单
        WebDriverWait(driver, 10).until(
            EC.presence_of_element_located((By.ID, "email"))
        )
        driver.find_element(By.ID, "email").send_keys(register_email)
        driver.find_element(By.ID, "password").send_keys(password)
        driver.find_element(By.ID, "name").send_keys(name)
        driver.find_element(By.ID, "verifyPassword").send_keys(password)
        time.sleep(1.0)

        button = WebDriverWait(driver, 5).until(
                    EC.element_to_be_clickable(
                        (By.XPATH, "//button[text()='Create Account']")
                    )
                )
        button.click()

        time.sleep(5.0)
        if "Verification Code" in driver.page_source:
            logger.info(f"{register_email} 需要验证码")
            # 等待并获取验证码（从接收邮箱获取，但验证目标邮箱）
            verification_code = wait_for_verification_email(RECEIVE_EMAIL, register_email, max_wait_time=60)

            if verification_code:
                logger.info(f"{register_email} 的验证码是: {verification_code}")
                # 输入验证码
                for i, x in enumerate(verification_code):
                    driver.execute_script(
                        f"return document.getElementsByClassName('flex bg-background px-3 py-2 outline-none hover:border-input focus:ring-0 focus-within:border-blue-500 focus:border-2 focus-within:border-2 !focus:shadow-none placeholder:text-muted-foreground disabled:cursor-not-allowed disabled:opacity-50 transition-colors transition-border duration-200 box-border h-12 w-12 rounded-lg border border-gray-300 text-center text-xl font-medium focus:border-blue-500 focus:ring-blue-500')[{i}]"
                    ).send_keys(x)
            else:
                logger.error(f"{register_email} 未能获取到验证码")
                return {"success": False, "email": register_email, "error": "未能获取验证码"}

        # 点击创建Verify Email
        attempt_count = 0
        max_attempts = 10
        wait_time = 1.0
        max_wait_time = 15.0

        while "onboarding" not in driver.current_url and attempt_count < max_attempts:
            try:
                button = WebDriverWait(driver, 5).until(
                    EC.element_to_be_clickable(
                        (By.XPATH, "//button[text()='Verify Email']")
                    )
                )
                button.click()

                # 等待URL变化或超时
                try:
                    WebDriverWait(driver, wait_time).until(
                        lambda d: "onboarding" in d.current_url
                    )
                except:
                    # 超时后继续循环
                    pass

            except Exception as e:
                logger.warning(
                    f"点击创建账户按钮失败 (尝试 {attempt_count+1}/{max_attempts}): {str(e)}"
                )

            # 更新尝试次数和等待时间
            attempt_count += 1
            # 使用较温和的退避策略
            wait_time = min(1.0 * (1.5**attempt_count), max_wait_time)       

        # 检查是否成功进入 onboarding 页面
        if "onboarding" in driver.current_url:
            logger.info(f"{register_email} 注册成功，已进入 onboarding 页面")

            # 尝试登录获取 token
            token_data = login(register_email, password)

            if "access_token" in token_data:
                access_token = token_data["access_token"]
                account_info = f"{register_email}----{password}----{access_token}"

                # 同步写入文件（每个进程独立写入，不使用共享锁）
                save_result = save_account_info(account_info)
                if save_result:
                    logger.info(f"账户信息已保存: {register_email}")

                return {
                    "success": True,
                    "email": register_email,
                    "password": password,
                    "access_token": access_token,
                }
            else:
                logger.warning(
                    f"无法获取访问令牌: {token_data.get('error', '未知错误')}"
                )
        else:
            logger.warning(f"{register_email} 注册可能失败，未能进入 onboarding 页面")

        return {"success": False, "email": register_email}

    except Exception as e:
        logger.error(f"注册 {register_email} 过程中发生错误: {str(e)}")
        return {"success": False, "email": register_email, "error": str(e)}
    finally:
        # 确保浏览器关闭
        try:
            if driver:
                driver.quit()
        except Exception as e:
            logger.error(f"关闭浏览器时出错: {str(e)}")


def main(total_registrations=10, concurrent_processes=3):
    """主函数：并发执行注册任务"""
    # 确保输出文件目录存在
    output_dir = os.path.dirname(os.path.abspath("promptlayer.txt"))
    os.makedirs(output_dir, exist_ok=True)

    logger.info(
        f"开始并发注册 {total_registrations} 个账户，并发数: {concurrent_processes}"
    )

    # 创建进程池
    with multiprocessing.Pool(processes=concurrent_processes) as pool:
        # 使用range作为简单的任务索引
        results = pool.map(register, range(total_registrations))

    # 统计结果
    success_count = sum(1 for result in results if result.get("success", False))
    logger.info(f"注册任务完成。成功: {success_count}/{total_registrations}")

    # 返回详细结果
    return results


if __name__ == "__main__":

    # Windows 多进程支持
    multiprocessing.freeze_support()

    # 并发数和总注册数
    concurrent_processes = 1  # 调整为适合系统资源的数量
    total_registrations = 1

    main(total_registrations, concurrent_processes)